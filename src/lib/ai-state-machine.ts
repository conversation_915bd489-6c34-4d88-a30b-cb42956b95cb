// AI助手状态机定义
export type AIState = 
  | 'before-input'           // 初始状态：等待用户输入
  | 'input-on-focus'         // 聚焦状态：用户点击输入框
  | 'prompt-entered'         // 输入完成：用户填写了内容
  | 'submitting-state'       // 提交中：正在发送给AI
  | 'generating'             // 生成中：AI正在生成响应
  | 'generating-with-ai-question' // AI询问：AI提出问题等待用户选择
  | 'thread-continuation'    // Thread模式：用户选择继续对话
  | 'thread-input-filled'    // Thread输入：用户在Thread中输入了内容
  | 'prompt-sending-process' // Thread提交：Thread内容提交中
  | 'conversation-collapsed' // 对话折叠：内容已生成，对话被折叠
  | 'ai-generating-structured-content' // 结构化生成：AI生成结构化内容
  | 'generation-completed'   // 生成完成：最终状态

// 状态转换事件
export type AIEvent = 
  | 'FOCUS_INPUT'
  | 'ENTER_TEXT' 
  | 'BLUR_INPUT'
  | 'SUBMIT_PROMPT'
  | 'AI_START_GENERATING'
  | 'AI_ASK_QUESTION'
  | 'USER_SELECT_THREAD'
  | 'ENTER_THREAD_TEXT'
  | 'SUBMIT_THREAD'
  | 'COLLAPSE_CONVERSATION'
  | 'START_STRUCTURED_GENERATION'
  | 'COMPLETE_GENERATION'

// 单个块的状态
export interface BlockState {
  id: string;
  state: AIState;
  userInput: string;
  aiResponse: string;
  isInputFocused: boolean;
  isSubmitting: boolean;
  isGenerating: boolean;
  timestamp: number;
}

// 应用数据状态
export interface AppState {
  // 多个独立的块
  blocks: BlockState[];
  
  // 当前活跃的块ID
  activeBlockId: string | null;
  
  // UI状态
  isCollapsed: boolean;
  
  // 对话历史
  conversationHistory: ConversationItem[];
}

export interface ConversationItem {
  type: 'user' | 'ai' | 'question';
  content: string;
  timestamp: number;
}

export interface StructuredContent {
  title: string;
  sections: ContentSection[];
  codeBlocks: CodeBlock[];
}

export interface ContentSection {
  type: 'h1' | 'h2' | 'ul' | 'p';
  content: string;
  emoji?: string;
}

export interface CodeBlock {
  language: string;
  code: string;
  filename?: string;
}

// 创建新块的工厂函数
export function createNewBlock(userInput: string = ''): BlockState {
  return {
    id: `block-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    state: userInput ? 'prompt-entered' : 'before-input',
    userInput,
    aiResponse: '',
    isInputFocused: false,
    isSubmitting: false,
    isGenerating: false,
    timestamp: Date.now()
  };
}

// 多块状态管理
export class MultiBlockStateMachine {
  private appState: AppState;
  private listeners: ((appState: AppState) => void)[] = [];

  constructor() {
    this.appState = {
      blocks: [createNewBlock()],
      activeBlockId: null,
      isCollapsed: false,
      conversationHistory: []
    };
  }

  getAppState(): AppState {
    return this.appState;
  }

  getBlockById(blockId: string): BlockState | undefined {
    return this.appState.blocks.find(block => block.id === blockId);
  }

  updateBlock(blockId: string, updates: Partial<BlockState>): void {
    const blockIndex = this.appState.blocks.findIndex(block => block.id === blockId);
    if (blockIndex !== -1) {
      this.appState.blocks[blockIndex] = {
        ...this.appState.blocks[blockIndex],
        ...updates,
        timestamp: Date.now()
      };
      this.notifyListeners();
    }
  }

  addNewBlock(userInput: string = ''): string {
    const newBlock = createNewBlock(userInput);
    this.appState.blocks.push(newBlock);
    this.appState.activeBlockId = newBlock.id;
    this.notifyListeners();
    return newBlock.id;
  }

  setActiveBlock(blockId: string | null): void {
    this.appState.activeBlockId = blockId;
    this.notifyListeners();
  }

  removeBlock(blockId: string): void {
    this.appState.blocks = this.appState.blocks.filter(block => block.id !== blockId);
    if (this.appState.activeBlockId === blockId) {
      this.appState.activeBlockId = this.appState.blocks.length > 0 ? this.appState.blocks[0].id : null;
    }
    this.notifyListeners();
  }

  transition(blockId: string, event: AIEvent): void {
    const block = this.getBlockById(blockId);
    if (!block) return;

    const newState = this.getNextState(block.state, event);
    if (newState !== block.state) {
      this.updateBlock(blockId, { state: newState });
    }
  }

  subscribe(listener: (appState: AppState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.appState));
  }

  private getNextState(currentState: AIState, event: AIEvent): AIState {
    const transitions: Record<AIState, Partial<Record<AIEvent, AIState>>> = {
      'before-input': {
        'FOCUS_INPUT': 'input-on-focus'
      },
      'input-on-focus': {
        'ENTER_TEXT': 'prompt-entered',
        'BLUR_INPUT': 'before-input'
      },
      'prompt-entered': {
        'SUBMIT_PROMPT': 'submitting-state',
        'BLUR_INPUT': 'before-input'
      },
      'submitting-state': {
        'AI_START_GENERATING': 'generating'
      },
      'generating': {
        'AI_ASK_QUESTION': 'generating-with-ai-question',
        'START_STRUCTURED_GENERATION': 'ai-generating-structured-content'
      },
      'generating-with-ai-question': {
        'USER_SELECT_THREAD': 'thread-continuation'
      },
      'thread-continuation': {
        'ENTER_THREAD_TEXT': 'thread-input-filled'
      },
      'thread-input-filled': {
        'SUBMIT_THREAD': 'prompt-sending-process'
      },
      'prompt-sending-process': {
        'COLLAPSE_CONVERSATION': 'conversation-collapsed'
      },
      'conversation-collapsed': {
        'START_STRUCTURED_GENERATION': 'ai-generating-structured-content'
      },
      'ai-generating-structured-content': {
        'COMPLETE_GENERATION': 'generation-completed'
      },
      'generation-completed': {}
    };

    return transitions[currentState][event] || currentState;
  }
}