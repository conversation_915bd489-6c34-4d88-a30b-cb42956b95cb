'use client';

import { AppState, MultiBlockStateMachine } from '@/lib/ai-state-machine';
import { MainContentArea } from './MainContentArea';

interface AIContentAreaProps {
  gid: string;
  stateMachine: MultiBlockStateMachine;
  appState: AppState;
  onAppStateUpdate: (appState: AppState) => void;
}

/**
 * AI内容区域组件 - 基于多块状态机驱动的动态内容渲染
 * 支持多个独立的prompt-response块
 */
export function AIContentArea({ 
  gid, 
  stateMachine,
  appState, 
  onAppStateUpdate 
}: AIContentAreaProps) {
  return (
    <MainContentArea
      gid={gid}
      stateMachine={stateMachine}
      appState={appState}
      onAppStateUpdate={onAppStateUpdate}
    />
  );
}