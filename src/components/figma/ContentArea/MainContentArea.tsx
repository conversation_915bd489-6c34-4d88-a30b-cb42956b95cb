'use client';

import { useRef, useEffect } from 'react';
import { AIState, AIEvent, AppState, BlockState, MultiBlockStateMachine } from '@/lib/ai-state-machine';

interface MainContentAreaProps {
  gid: string;
  stateMachine: MultiBlockStateMachine;
  appState: AppState;
  onAppStateUpdate: (appState: AppState) => void;
}

/**
 * 主内容区域组件 - 基于Figma设计实现真实的AI交互界面
 * 支持多个独立的prompt-response块
 */
export function MainContentArea({ 
  gid, 
  stateMachine,
  appState,
  onAppStateUpdate
}: MainContentAreaProps) {
  // 判断是否有任何块需要显示侧边栏
  const shouldShowSidebar = appState.blocks.some(block => 
    block.state === 'generating' || 
    block.state === 'generating-with-ai-question' ||
    block.state === 'ai-generating-structured-content' ||
    block.state === 'generation-completed'
  );

  // 输入区域的引用映射
  const inputAreaRefs = useRef<{[blockId: string]: HTMLDivElement | null}>({});

  // 处理块的输入框聚焦
  const handleInputFocus = (blockId: string) => {
    stateMachine.transition(blockId, 'FOCUS_INPUT');
    stateMachine.updateBlock(blockId, { isInputFocused: true });
  };

  // 处理块的输入框失去焦点
  const handleInputBlur = (blockId: string) => {
    const block = stateMachine.getBlockById(blockId);
    if (block && !block.userInput.trim()) {
      stateMachine.transition(blockId, 'BLUR_INPUT');
      stateMachine.updateBlock(blockId, { isInputFocused: false });
    }
  };

  // 监听点击外部事件 - 对所有块生效
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      appState.blocks.forEach(block => {
        const inputRef = inputAreaRefs.current[block.id];
        if ((block.state === 'input-on-focus' || block.state === 'prompt-entered') && 
            inputRef && 
            !inputRef.contains(event.target as Node) &&
            !block.userInput.trim()) {
          handleInputBlur(block.id);
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [appState.blocks]);

  // 处理块的文本输入
  const handleTextChange = (blockId: string, text: string) => {
    const block = stateMachine.getBlockById(blockId);
    if (block) {
      stateMachine.updateBlock(blockId, { userInput: text });
      if (text.length > 0 && block.state === 'input-on-focus') {
        stateMachine.transition(blockId, 'ENTER_TEXT');
      }
    }
  };

  // 处理块的提交
  const handleSubmit = (blockId: string) => {
    const block = stateMachine.getBlockById(blockId);
    if (block && block.userInput.trim()) {
      stateMachine.transition(blockId, 'SUBMIT_PROMPT');
      stateMachine.updateBlock(blockId, { isSubmitting: true });
      
      // 模拟AI响应
      setTimeout(() => {
        stateMachine.transition(blockId, 'AI_START_GENERATING');
        stateMachine.updateBlock(blockId, { 
          isSubmitting: false, 
          isGenerating: true,
          aiResponse: "Develop a cross-platform Todo application using SQLite for local data storage and Passkey for secure, passwordless authentication..."
        });
      }, 1000);
    }
  };

  // 添加新块
  const handleAddNewBlock = () => {
    const newBlockId = stateMachine.addNewBlock();
    // 立即将新块设置为聚焦状态
    stateMachine.transition(newBlockId, 'FOCUS_INPUT');
    stateMachine.updateBlock(newBlockId, { isInputFocused: true });
    onAppStateUpdate(stateMachine.getAppState());
    return newBlockId;
  };

  // 判断是否应该显示新块的占位符
  const shouldShowNewBlockPlaceholder = () => {
    // 只有当有块正在生成时才显示新的占位符
    return appState.blocks.some(block =>
      block.state === 'generating' ||
      block.state === 'submitting-state' ||
      block.state === 'generating-with-ai-question' ||
      block.state === 'ai-generating-structured-content'
    );
  };

  return (
    <div className={`w-full h-full flex flex-col items-start justify-start min-h-screen ${
      shouldShowSidebar && !appState.isCollapsed
        ? 'pl-[40px]'
        : ''
    }`}>
      {/* 调试信息 */}
      <div className="text-white text-sm p-2 bg-neutral-800 rounded mb-4">
        Blocks: {appState.blocks.length} | Sidebar: {shouldShowSidebar ? 'Yes' : 'No'} | Active: {appState.activeBlockId || 'None'}
      </div>
      
      {/* 多个内容块 - 固定宽度800px */}
      <div className="relative shrink-0 w-[800px] space-y-6">
        {appState.blocks.map((block, index) => (
          <div key={block.id} className="relative">
            {renderBlockContent(block)}
          </div>
        ))}

        {/* 只在有块正在生成时显示新的Placeholder区域 */}
        {shouldShowNewBlockPlaceholder() && (
          <div className="mt-6">
            {renderAddNewBlockPlaceholder()}
          </div>
        )}
      </div>
    </div>
  );

  // 根据块的状态渲染对应的内容
  function renderBlockContent(block: BlockState) {
    switch (block.state) {
      case 'before-input':
        return renderBeforeInputState(block);
      
      case 'input-on-focus':
      case 'prompt-entered':
        return renderInputFocusState(block);
      
      case 'submitting-state':
        return renderSubmittingState(block);
      
      case 'generating':
        return renderGeneratingState(block);
      
      default:
        return renderBeforeInputState(block);
    }
  }

  // 状态1: 初始输入状态
  function renderBeforeInputState(block: BlockState) {
    return (
      <div className="w-full relative">
        {/* Block容器 - 响应式宽度，相对定位，flex布局确保同一行 */}
        <div 
          ref={(el) => { inputAreaRefs.current[block.id] = el; }}
          className="relative shrink-0 w-full h-6 flex items-center"
        >
          {/* MenuTrigger - 左侧图标组 */}
          <div className="bg-neutral-950 h-6 rounded-lg px-1 flex items-center gap-1 mr-2">
            {/* TriggerIcon1 - 主触发图标 */}
            <div className="w-4 h-4 flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 12 12">
                <rect x="1" y="2" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="5.25" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="8.5" width="10" height="1.5" rx="0.75"/>
              </svg>
            </div>
            
            {/* SeparatorIcon - 分隔符，旋转90度 */}
            <div className="flex-none rotate-90">
              <div className="w-3 h-px bg-neutral-400"></div>
            </div>
            
            {/* TriggerIcon2 - 拖拽图标 */}
            <div className="w-2 h-2.5 flex items-center justify-center">
              <svg className="w-2 h-2.5 text-white" fill="currentColor" viewBox="0 0 8 10">
                <circle cx="2" cy="2" r="0.75"/>
                <circle cx="6" cy="2" r="0.75"/>
                <circle cx="2" cy="5" r="0.75"/>
                <circle cx="6" cy="5" r="0.75"/>
                <circle cx="2" cy="8" r="0.75"/>
                <circle cx="6" cy="8" r="0.75"/>
              </svg>
            </div>
          </div>
          
          {/* TextContainer - 文本输入区域，flex-1占据剩余空间 */}
          <div 
            className="flex-1 relative cursor-text h-6 flex items-center"
            onClick={() => handleInputFocus(block.id)}
          >
            <p className="font-inter font-normal text-[16px] text-left text-neutral-600">
              Write, press 'space' for AI
            </p>
            {/* 光标 - 绝对定位 */}
            {block.isInputFocused && (
              <div className="absolute h-4 left-1 top-1 w-px bg-white animate-pulse"></div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // 状态2-3: 输入框聚焦和输入完成状态
  function renderInputFocusState(block: BlockState) {
    const isPromptEntered = block.state === 'prompt-entered';
    
    return (
      <div className="basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0">
        <div 
          ref={(el) => { inputAreaRefs.current[block.id] = el; }}
          className={`bg-neutral-950 relative rounded-2xl shrink-0 w-full p-4 border-2 ${
            block.isInputFocused ? 'border-sky-500' : 'border-transparent'
          }`}
        >
          {/* 文本输入容器 */}
          <div className="box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0 w-full mb-4">
            <input
              type="text"
              value={block.userInput}
              onChange={(e) => handleTextChange(block.id, e.target.value)}
              onFocus={() => handleInputFocus(block.id)}
              placeholder="Tell me what you want to do"
              className="flex-1 bg-transparent border-none outline-none text-[16px] text-neutral-300 placeholder-neutral-600 font-inter font-normal"
              autoFocus={block.isInputFocused}
            />
          </div>
          
          {/* 按钮容器 */}
          <div className="box-border content-stretch flex flex-row gap-2 items-start justify-start p-0 relative shrink-0 w-full">
            {/* 操作按钮 1 - Plus 图标 */}
            <button className="relative rounded-lg shrink-0 border border-neutral-600 p-2 w-8 h-8 flex items-center justify-center hover:border-neutral-500">
              <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
              </svg>
            </button>
            
            {/* 操作按钮 2 - Attachment 图标 */}
            <button className="relative rounded-lg shrink-0 border border-neutral-700 p-2 w-8 h-8 flex items-center justify-center hover:border-neutral-600">
              <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                <path d="M4.5 3a2.5 2.5 0 0 1 5 0v9a1.5 1.5 0 0 1-3 0V5a.5.5 0 0 1 1 0v7a.5.5 0 0 0 1 0V3a1.5 1.5 0 1 0-3 0v9a2.5 2.5 0 0 0 5 0V5a.5.5 0 0 1 1 0v7a3.5 3.5 0 1 1-7 0V3z"/>
              </svg>
            </button>
            
            {/* 弹性空间 */}
            <div className="basis-0 grow min-h-px min-w-px"></div>
            
            {/* 提交按钮 */}
            <button
              onClick={() => handleSubmit(block.id)}
              disabled={!block.userInput.trim() || block.isSubmitting}
              className={`relative rounded-lg shrink-0 p-2 w-8 h-8 flex items-center justify-center ${
                block.userInput.trim() && !block.isSubmitting
                  ? 'bg-gradient-to-r from-sky-500 to-blue-500 border-sky-400'
                  : 'bg-neutral-800 border-neutral-700'
              } border transition-all duration-200`}
            >
              {block.isSubmitting ? (
                <div className="w-3 h-3 border border-neutral-300 border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 16 16">
                  <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 状态4: 提交中状态
  function renderSubmittingState(block: BlockState) {
    return (
      <div className="basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0">
        <div className="bg-neutral-950 relative rounded-2xl shrink-0 w-full p-4 border-2 border-sky-500 opacity-60">
          {/* 禁用的文本输入 */}
          <div className="box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0 w-full mb-4">
            <p className="flex-1 text-[16px] text-neutral-600 font-inter font-normal">
              {block.userInput}
            </p>
          </div>
          
          {/* 禁用的按钮 */}
          <div className="box-border content-stretch flex flex-row gap-2 items-start justify-start p-0 relative shrink-0 w-full">
            <button disabled className="relative rounded-lg shrink-0 border border-neutral-700 p-2 w-8 h-8 flex items-center justify-center opacity-30">
              <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
              </svg>
            </button>
            <button disabled className="relative rounded-lg shrink-0 border border-neutral-700 p-2 w-8 h-8 flex items-center justify-center opacity-30">
              <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                <path d="M4.5 3a2.5 2.5 0 0 1 5 0v9a1.5 1.5 0 0 1-3 0V5a.5.5 0 0 1 1 0v7a.5.5 0 0 0 1 0V3a1.5 1.5 0 1 0-3 0v9a2.5 2.5 0 0 0 5 0V5a.5.5 0 0 1 1 0v7a3.5 3.5 0 1 1-7 0V3z"/>
              </svg>
            </button>
            <div className="basis-0 grow min-h-px min-w-px"></div>
            <button disabled className="relative rounded-lg shrink-0 p-2 w-8 h-8 flex items-center justify-center bg-neutral-800 border border-neutral-700">
              <div className="w-3 h-3 border border-neutral-300 border-t-transparent rounded-full animate-spin"></div>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 状态5: AI生成状态
  function renderGeneratingState(block: BlockState) {
    return (
      <div className="space-y-6">
        {/* Prompt 完成面板 */}
        <div className="relative shrink-0 w-[800px]">
          <div className="basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0">
            <div 
              className="relative rounded-2xl shrink-0 w-full p-4"
              style={{ background: 'rgba(255,255,255,0.03)' }}
            >
              {/* Prompt 信息头部 */}
              <div className="box-border content-stretch flex flex-row gap-1 items-start justify-start p-0 relative shrink-0 w-full mb-2">
                <p className="basis-0 font-inter font-medium grow leading-[18px] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-600">
                  Prompt:
                </p>
                <div className="box-border content-stretch flex flex-row gap-1 items-center justify-start p-0 relative shrink-0">
                  <p className="font-inter font-medium leading-[18px] not-italic relative shrink-0 text-[14px] text-left text-neutral-50 text-nowrap">
                    Worked for 1m 22s
                  </p>
                  <svg className="overflow-clip relative shrink-0 size-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                    <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
                  </svg>
                </div>
              </div>
              
              {/* Prompt 文本 */}
              <p className="font-inter font-normal leading-[28px] not-italic relative shrink-0 text-[16px] text-left text-neutral-400 w-full">
                {block.userInput}
              </p>
            </div>
          </div>
        </div>

        {/* 代码生成块 */}
        <div className="relative shrink-0 w-[800px]">
          <div className="basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0">
            <div className="bg-neutral-950 relative rounded-2xl shrink-0 w-full">
              {/* 代码头部 */}
              <div className="h-12 relative shrink-0 w-full p-4 flex items-center justify-between">
                <div className="basis-0 box-border content-stretch flex flex-row gap-1 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
                  {/* 加载图标 */}
                  <div className="overflow-clip relative shrink-0 size-6 mr-2">
                    <div 
                      className="absolute left-1/2 overflow-clip size-4 top-1/2"
                      style={{ transform: 'translate(-50%, -50%) rotate(90deg)' }}
                    >
                      <svg className="w-4 h-4 animate-spin text-neutral-400" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m12 2 A10 10 0 0 1 22 12 h-4 A6 6 0 0 0 12 6 V2 Z"></path>
                      </svg>
                    </div>
                  </div>
                  
                  {/* 渐变标题 */}
                  <p 
                    className="font-inter font-medium leading-[20px] not-italic relative shrink-0 text-[16px] text-left text-nowrap"
                    style={{
                      background: 'linear-gradient(to right, #fafafa, #404040)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}
                  >
                    Build a Todo App with SQLite and Passkey Authentication
                  </p>
                </div>
                
                <svg className="overflow-clip relative shrink-0 size-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                  <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
                </svg>
              </div>
              
              {/* 生成的内容 */}
              <div className="relative shrink-0 w-full p-4">
                <p className="font-inter font-normal leading-[28px] min-w-full not-italic relative shrink-0 text-[16px] text-left text-neutral-50">
                  {block.aiResponse}
                  {/* 文本光标 */}
                  <span className="inline-block w-2 h-4 bg-neutral-50 ml-1 animate-pulse"></span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 渲染添加新块的Placeholder区域 - 与before-input状态完全一致
  function renderAddNewBlockPlaceholder() {
    return (
      <div className="w-full relative">
        {/* Block容器 - 响应式宽度，相对定位，flex布局确保同一行 */}
        <div
          className="relative shrink-0 w-full h-6 flex items-center"
          onClick={handleAddNewBlock}
        >
          {/* MenuTrigger - 左侧图标组 */}
          <div className="bg-neutral-950 h-6 rounded-lg px-1 flex items-center gap-1 mr-2">
            {/* TriggerIcon1 - 主触发图标 */}
            <div className="w-4 h-4 flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 12 12">
                <rect x="1" y="2" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="5.25" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="8.5" width="10" height="1.5" rx="0.75"/>
              </svg>
            </div>

            {/* SeparatorIcon - 分隔符，旋转90度 */}
            <div className="flex-none rotate-90">
              <div className="w-3 h-px bg-neutral-400"></div>
            </div>

            {/* TriggerIcon2 - 拖拽图标 */}
            <div className="w-2 h-2.5 flex items-center justify-center">
              <svg className="w-2 h-2.5 text-white" fill="currentColor" viewBox="0 0 8 10">
                <circle cx="2" cy="2" r="0.75"/>
                <circle cx="6" cy="2" r="0.75"/>
                <circle cx="2" cy="5" r="0.75"/>
                <circle cx="6" cy="5" r="0.75"/>
                <circle cx="2" cy="8" r="0.75"/>
                <circle cx="6" cy="8" r="0.75"/>
              </svg>
            </div>
          </div>

          {/* TextContainer - 文本输入区域，flex-1占据剩余空间 */}
          <div className="flex-1 relative cursor-text h-6 flex items-center">
            <p className="font-inter font-normal text-[16px] text-left text-neutral-600">
              Write, press 'space' for AI
            </p>
          </div>
        </div>
      </div>
    );
  }
}